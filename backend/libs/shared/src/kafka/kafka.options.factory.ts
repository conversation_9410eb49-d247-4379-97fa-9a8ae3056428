import { Injectable } from '@nestjs/common';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import { AppConfigService } from '../config';

@Injectable()
export class KafkaServerOptionsFactory {
    constructor(private readonly cfg: AppConfigService) { }

    create(): MicroserviceOptions {
        return {
            transport: Transport.KAFKA,
            options: {
                client: {
                    clientId: this.cfg.kafkaClientId,
                    brokers: this.cfg.kafkaBrokers,
                    connectionTimeout: this.cfg.kafkaConnectionTimeout,
                    requestTimeout: this.cfg.kafkaRequestTimeout,
                    // KafkaJS retry settings (valid, type-safe)
                    retry: {
                        // Number of retries when connecting/communicating
                        retries: this.cfg.kafkaRetryAttempts,
                        // Backoff start (ms). KafkaJS will back off exponentially by default.
                        initialRetryTime: this.cfg.kafkaRetryDelay,
                        // You can also set maxRetryTime, factor, multiplier if you like.
                        // maxRetryTime: 30000,
                        // factor: 0.2,
                    },
                },
                consumer: {
                    groupId: this.cfg.kafkaGroupId,
                    // If you want per-consumer retry override, KafkaJS allows it here too:
                    // retry: {
                    //   retries: this.cfg.kafkaRetryAttempts,
                    //   initialRetryTime: this.cfg.kafkaRetryDelay,
                    // },
                },
                // Optionally:
                // run: { autoCommit: true }, // or fine-tune commit strategy
                // subscribe: { fromBeginning: false },
                // producerOnlyMode: false,
                // serializer, deserializer: custom serializers if needed
            },
        };
    }
}

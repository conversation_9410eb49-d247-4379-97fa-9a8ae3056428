import { Injectable } from '@nestjs/common';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import { AppConfigService } from '../config';

@Injectable()
export class RmqServerOptionsFactory {
    constructor(private readonly cfg: AppConfigService) { }

    create(): MicroserviceOptions {
        return {
            transport: Transport.RMQ,
            options: {
                urls: [this.cfg.rabbitmqUrl],
                queue: this.cfg.rabbitmqQueue,
                queueOptions: {
                    durable: this.cfg.rabbitmqDurable,
                    arguments: {
                        'x-message-ttl': 300_000, // 5 minutes
                        // DLX/DLQ: provision `${exchange}.dlx` and bind `${routingKey}.dead`
                        'x-dead-letter-exchange': `${this.cfg.rabbitmqExchange}.dlx`,
                        'x-dead-letter-routing-key': `${this.cfg.rabbitmqRoutingKey}.dead`,
                    },
                },
                socketOptions: {
                    heartbeatIntervalInSeconds: 60,
                    reconnectTimeInSeconds: 5,
                },
                prefetchCount: this.cfg.rabbitmqPrefetchCount,
                isGlobalPrefetchCount: false,
                noAck: false,
            },
        };
    }
}

// Mask amqp(s)://user:pass@host
export function maskRabbitUrl(url: string): string {
    return url.replace(/(amqps?:\/\/)([^:@/]+):([^@/]+)@/, (_, p1) => `${p1}***:***@`);
}

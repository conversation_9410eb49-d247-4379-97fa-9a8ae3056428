# API Configuration
NODE_ENV=development
CORE_API_PORT=3000
CORE_API_HOST=localhost
CORE_API_PREFIX=api
CORE_API_VERSION=1
# Database Configuration


# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379

# Logging
LOG_LEVEL=debug
# database connection URL
DATABASE_URL="postgresql://tukxi_db_user:password@localhost:5432/tukxi"
DATABASE_NAME=tukxi
DATABASE_USER=tukxi_db_user
DATABASE_PASSWORD=password
DATABASE_HOST=localhost
DATABASE_PORT=5432

# engagespot
ENGAGESPOT_API_KEY='your_engagespot_api_key'
ENGAGESPOT_API_SECRET='your_engagespot_api_secret'

EN_VERIFICATION_CODE_WORKFLOW='driver_verification_code_login'
EN_EMAIL_VERIFICATION_CODE_WORKFLOW='email-verification'
EN_FORGOT_PASSWORD_WORKFLOW='forgot_password'
# JWT Configuration
JWT_SECRET='your_jwt_secret_key_with_at_least_32_characters_long'
JWT_ACCESS_TOKEN_EXPIRY='15m'
JWT_REFRESH_TOKEN_EXPIRY='7d'

# Google OAuth Configuration
GOOGLE_CLIENT_ID='your_google_client_id'
GOOGLE_CLIENT_SECRET='your_google_client_secret'
GOOGLE_CLIENT_ID_IOS='your_google_client_id_ios'

# Apple OAuth Configuration
APPLE_CLIENT_ID='your_apple_client_id'
APPLE_TEAM_ID='your_apple_team_id'
APPLE_KEY_ID='your_apple_key_id'
APPLE_PRIVATE_KEY='your_apple_private_key'
APPLE_BUNDLE_ID='your_apple_bundle_id'

# OTP Configuration
OTP_LENGTH=4
OTP_EXPIRY_MINUTES=5
OTP_WINDOW=0

# Rate Limiting Configuration
AUTH_RATE_LIMIT_TTL=900
AUTH_RATE_LIMIT_MAX=5

# AWS Configuration
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
AWS_REGION=us-east-1
AWS_BUCKET_NAME=your_aws_bucket_name

# Cashfree Configuration
CASHFREE_CLIENT_SECRET=your_cash_free_client_secret
CASHFREE_CLIENT_ID=your_cash_free_client_id
# CASHFREE_ENVIRONMENT=production
CASHFREE_ENVIRONMENT=sandbox

# Microservices Configuration

# Kafka Configuration
KAFKA_BROKERS=localhost:9092
KAFKA_CLIENT_ID=tukxi-kafka-client
KAFKA_GROUP_ID=tukxi-consumer-group
KAFKA_RETRY_ATTEMPTS=5
KAFKA_RETRY_DELAY=1000
KAFKA_CONNECTION_TIMEOUT=10000
KAFKA_REQUEST_TIMEOUT=30000

# RabbitMQ Configuration
RABBITMQ_URL=amqp://localhost:5672
RABBITMQ_QUEUE=tukxi_queue
RABBITMQ_EXCHANGE=tukxi_exchange
RABBITMQ_ROUTING_KEY=tukxi.ride.match
RABBITMQ_DURABLE=true
RABBITMQ_PREFETCH_COUNT=10

# Microservice Ports
KAFKA_CONSUMER_PORT=3001
NOTIFIER_PORT=3002
RIDE_MATCHER_PORT=3003

# Health Check Configuration
HEALTH_CHECK_TIMEOUT=5000
HEALTH_CHECK_INTERVAL=30000

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json

# Graceful Shutdown Configuration
SHUTDOWN_TIMEOUT=10000


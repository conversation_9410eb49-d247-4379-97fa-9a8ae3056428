import { Test, TestingModule } from '@nestjs/testing';
import { KafkaConsumersController } from './kafka-consumers.controller';
import { KafkaConsumersService } from './kafka-consumers.service';

describe('KafkaConsumersController', () => {
  let kafkaConsumersController: KafkaConsumersController;

  beforeEach(async () => {
    const app: TestingModule = await Test.createTestingModule({
      controllers: [KafkaConsumersController],
      providers: [KafkaConsumersService],
    }).compile();

    kafkaConsumersController = app.get<KafkaConsumersController>(KafkaConsumersController);
  });

  describe('root', () => {
    it('should return "Hello World!"', () => {
      expect(kafkaConsumersController.getHello()).toBe('Hello World!');
    });
  });
});

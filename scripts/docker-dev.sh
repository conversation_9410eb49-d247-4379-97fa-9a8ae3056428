#!/bin/bash

# Docker Development Environment Management Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Dock<PERSON> is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
}

# Function to start all services
start_all() {
    print_status "Starting all services in development mode..."
    docker compose up -d
    print_success "All services started successfully!"
}

# Function to start only infrastructure services
start_infra() {
    print_status "Starting infrastructure services only..."
    docker-compose up -d pgsql redis zookeeper kafka rabbitmq
    print_success "Infrastructure services started successfully!"
}

# Function to start specific microservices
start_microservices() {
    print_status "Starting microservices..."
    docker-compose up -d kafka-consumers notifier ride-matcher
    print_success "Microservices started successfully!"
}

# Function to stop all services
stop_all() {
    print_status "Stopping all services..."
    docker-compose down
    print_success "All services stopped successfully!"
}

# Function to restart all services
restart_all() {
    print_status "Restarting all services..."
    docker compose down
    docker compose up -d
    print_success "All services restarted successfully!"
}

# Function to view logs
view_logs() {
    if [ -z "$1" ]; then
        print_status "Showing logs for all services..."
        docker-compose logs -f
    else
        print_status "Showing logs for service: $1"
        docker-compose logs -f "$1"
    fi
}

# Function to show service status
show_status() {
    print_status "Service status:"
    docker-compose ps
}

# Function to clean up
cleanup() {
    print_warning "This will remove all containers, networks, and volumes. Are you sure? (y/N)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        print_status "Cleaning up Docker environment..."
        docker compose down -v --remove-orphans
        docker system prune -f
        print_success "Cleanup completed!"
    else
        print_status "Cleanup cancelled."
    fi
}

# Function to rebuild services
rebuild() {
    if [ -z "$1" ]; then
        print_status "Rebuilding all services..."
        docker compose build --no-cache
        docker compose up -d
    else
        print_status "Rebuilding service: $1"
        docker compose build --no-cache "$1"
        docker compose up -d "$1"
    fi
    print_success "Rebuild completed!"
}

# Function to show help
show_help() {
    echo "Docker Development Environment Management"
    echo ""
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  start           Start all services"
    echo "  start-infra     Start only infrastructure services (DB, Redis, Kafka, RabbitMQ)"
    echo "  start-micro     Start only microservices"
    echo "  stop            Stop all services"
    echo "  restart         Restart all services"
    echo "  logs [service]  View logs (all services or specific service)"
    echo "  status          Show service status"
    echo "  rebuild [service] Rebuild and restart services"
    echo "  cleanup         Remove all containers, networks, and volumes"
    echo "  help            Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 start                    # Start all services"
    echo "  $0 logs api                 # View API service logs"
    echo "  $0 rebuild kafka-consumers  # Rebuild kafka-consumers service"
}

# Main script logic
check_docker

case "${1:-help}" in
    start)
        start_all
        ;;
    start-infra)
        start_infra
        ;;
    start-micro)
        start_microservices
        ;;
    stop)
        stop_all
        ;;
    restart)
        restart_all
        ;;
    logs)
        view_logs "$2"
        ;;
    status)
        show_status
        ;;
    rebuild)
        rebuild "$2"
        ;;
    cleanup)
        cleanup
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        print_error "Unknown command: $1"
        show_help
        exit 1
        ;;
esac

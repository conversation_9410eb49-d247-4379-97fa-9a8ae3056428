#!/bin/bash

# Docker Production Environment Management Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Dock<PERSON> is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
}

# Function to start all services in production mode
start_all() {
    print_status "Starting all services in production mode..."
    docker compose -f docker-compose.prod.yml up -d
    print_success "All services started successfully!"
}

# Function to start only infrastructure services
start_infra() {
    print_status "Starting infrastructure services only..."
    docker compose -f docker-compose.prod.yml up -d pgsql redis zookeeper kafka rabbitmq
    print_success "Infrastructure services started successfully!"
}

# Function to start specific microservices
start_microservices() {
    print_status "Starting microservices..."
    docker compose -f docker-compose.prod.yml up -d kafka-consumers notifier ride-matcher
    print_success "Microservices started successfully!"
}

# Function to stop all services
stop_all() {
    print_status "Stopping all services..."
    docker compose -f docker-compose.prod.yml down
    print_success "All services stopped successfully!"
}

# Function to restart all services
restart_all() {
    print_status "Restarting all services..."
    docker compose -f docker-compose.prod.yml down
    docker compose -f docker-compose.prod.yml up -d
    print_success "All services restarted successfully!"
}

# Function to view logs
view_logs() {
    if [ -z "$1" ]; then
        print_status "Showing logs for all services..."
        docker compose -f docker-compose.prod.yml logs -f
    else
        print_status "Showing logs for service: $1"
        docker compose -f docker-compose.prod.yml logs -f "$1"
    fi
}

# Function to show service status
show_status() {
    print_status "Service status:"
    docker compose -f docker-compose.prod.yml ps
}

# Function to scale services
scale_service() {
    if [ -z "$1" ] || [ -z "$2" ]; then
        print_error "Usage: scale <service> <replicas>"
        exit 1
    fi
    print_status "Scaling $1 to $2 replicas..."
    docker compose -f docker-compose.prod.yml up -d --scale "$1=$2"
    print_success "Service $1 scaled to $2 replicas!"
}

# Function to update services
update_services() {
    print_status "Updating services with zero-downtime deployment..."
    
    # Build new images
    print_status "Building new images..."
    docker compose -f docker-compose.prod.yml build --no-cache
    
    # Rolling update for each service
    services=("api" "kafka-consumers" "notifier" "ride-matcher")
    
    for service in "${services[@]}"; do
        print_status "Updating $service..."
        docker compose -f docker-compose.prod.yml up -d --no-deps "$service"
        
        # Wait for health check
        print_status "Waiting for $service to be healthy..."
        sleep 10
    done
    
    print_success "All services updated successfully!"
}

# Function to backup data
backup_data() {
    timestamp=$(date +%Y%m%d_%H%M%S)
    backup_dir="./backups/$timestamp"
    
    print_status "Creating backup directory: $backup_dir"
    mkdir -p "$backup_dir"
    
    # Backup PostgreSQL
    print_status "Backing up PostgreSQL database..."
    docker compose -f docker-compose.prod.yml exec -T pgsql pg_dump -U tukxi_db_user tukxi > "$backup_dir/postgres_backup.sql"
    
    # Backup Redis
    print_status "Backing up Redis data..."
    docker compose -f docker-compose.prod.yml exec -T redis redis-cli BGSAVE
    docker cp "$(docker compose -f docker-compose.prod.yml ps -q redis)":/data/dump.rdb "$backup_dir/redis_backup.rdb"
    
    print_success "Backup completed: $backup_dir"
}

# Function to show help
show_help() {
    echo "Docker Production Environment Management"
    echo ""
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  start           Start all services"
    echo "  start-infra     Start only infrastructure services"
    echo "  start-micro     Start only microservices"
    echo "  stop            Stop all services"
    echo "  restart         Restart all services"
    echo "  logs [service]  View logs (all services or specific service)"
    echo "  status          Show service status"
    echo "  scale <service> <replicas>  Scale a service"
    echo "  update          Update all services with zero-downtime"
    echo "  backup          Backup database and Redis data"
    echo "  help            Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 start                    # Start all services"
    echo "  $0 scale api 3              # Scale API service to 3 replicas"
    echo "  $0 logs kafka-consumers     # View kafka-consumers logs"
    echo "  $0 update                   # Update all services"
}

# Main script logic
check_docker

case "${1:-help}" in
    start)
        start_all
        ;;
    start-infra)
        start_infra
        ;;
    start-micro)
        start_microservices
        ;;
    stop)
        stop_all
        ;;
    restart)
        restart_all
        ;;
    logs)
        view_logs "$2"
        ;;
    status)
        show_status
        ;;
    scale)
        scale_service "$2" "$3"
        ;;
    update)
        update_services
        ;;
    backup)
        backup_data
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        print_error "Unknown command: $1"
        show_help
        exit 1
        ;;
esac

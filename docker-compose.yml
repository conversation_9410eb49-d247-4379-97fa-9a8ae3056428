version: '3.8'

services:
  # Core API Service
  api:
    build:
      context: ./
      dockerfile: ./docker/api.dev.Dockerfile
    volumes:
      - ./backend:/usr/src/app:delegated
      - /usr/src/app/node_modules  # isolate container node_modules
    ports:
      - '3000:3000'
    env_file:
      - ./backend/.env.docker
    depends_on:
      - pgsql
      - redis
      - kafka
      - rabbitmq
    networks:
      - tukxi-network

  # Kafka Consumers Microservice
  kafka-consumers:
    build:
      context: ./
      dockerfile: ./docker/kafka-consumers.dev.Dockerfile
    ports:
      - '3001:3001'
    env_file:
      - ./backend/.env.docker
    depends_on:
      - pgsql
      - redis
      - kafka
    networks:
      - tukxi-network
    restart: unless-stopped

  # Notifier Microservice
  notifier:
    build:
      context: ./
      dockerfile: ./docker/notifier.dev.Dockerfile
    ports:
      - '3002:3002'
    env_file:
      - ./backend/.env.docker
    depends_on:
      - pgsql
      - redis
    networks:
      - tukxi-network
    restart: unless-stopped

  # Ride Matcher Microservice
  ride-matcher:
    build:
      context: ./
      dockerfile: ./docker/ride-matcher.dev.Dockerfile
    ports:
      - '3003:3003'
    env_file:
      - ./backend/.env.docker
    depends_on:
      - pgsql
      - redis
      - rabbitmq
    networks:
      - tukxi-network
    restart: unless-stopped

  # Infrastructure Services

  # PostgreSQL Database
  pgsql:
    image: 'postgres:15-alpine'
    restart: always
    environment:
      POSTGRES_DB: 'tukxi'
      POSTGRES_USER: 'tukxi_db_user'
      POSTGRES_PASSWORD: 'password'
    volumes:
      - pgsql_data:/var/lib/postgresql/data
    ports:
      - '5432:5432'
    networks:
      - tukxi-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U tukxi_db_user -d tukxi"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: 'redis:7-alpine'
    restart: always
    ports:
      - '6379:6379'
    volumes:
      - redis_data:/data
    networks:
      - tukxi-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Zookeeper (required for Kafka)
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    restart: always
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    volumes:
      - zookeeper_data:/var/lib/zookeeper/data
      - zookeeper_logs:/var/lib/zookeeper/log
    networks:
      - tukxi-network

  # Kafka Message Broker
  kafka:
    image: confluentinc/cp-kafka:7.4.0
    restart: always
    depends_on:
      - zookeeper
    ports:
      - '9092:9092'
      - '29092:29092'
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092,PLAINTEXT_HOST://localhost:29092
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
      KAFKA_DELETE_TOPIC_ENABLE: 'true'
    volumes:
      - kafka_data:/var/lib/kafka/data
    networks:
      - tukxi-network
    healthcheck:
      test: ["CMD", "kafka-broker-api-versions", "--bootstrap-server", "localhost:9092"]
      interval: 30s
      timeout: 10s
      retries: 3

  # RabbitMQ Message Broker
  rabbitmq:
    image: 'rabbitmq:3.12-management-alpine'
    restart: always
    ports:
      - '5672:5672'   # AMQP port
      - '15672:15672' # Management UI port
    environment:
      RABBITMQ_DEFAULT_USER: 'tukxi'
      RABBITMQ_DEFAULT_PASS: 'password'
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - tukxi-network
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

# Named volumes for data persistence
volumes:
  pgsql_data:
    driver: local
  redis_data:
    driver: local
  zookeeper_data:
    driver: local
  zookeeper_logs:
    driver: local
  kafka_data:
    driver: local
  rabbitmq_data:
    driver: local

# Custom network for service communication
networks:
  tukxi-network:
    driver: bridge
